'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PreloaderProps {
  onComplete: () => void;
}

export default function Preloader({ onComplete }: PreloaderProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [greetingText, setGreetingText] = useState('');
  const [secondText, setSecondText] = useState('');

  const greeting = "Hi darling, welcome to Urvashi...";
  const secondLine = "Let's begin something special.";

  // Smooth typing effect for greeting
  useEffect(() => {
    if (currentStep === 0) {
      let index = 0;
      const timer = setInterval(() => {
        if (index <= greeting.length) {
          setGreetingText(greeting.slice(0, index));
          index++;
        } else {
          clearInterval(timer);
          setTimeout(() => setCurrentStep(1), 400); // Pause to read
        }
      }, 50); // Smooth readable speed
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  // Smooth typing effect for second line
  useEffect(() => {
    if (currentStep === 1) {
      let index = 0;
      const timer = setInterval(() => {
        if (index <= secondLine.length) {
          setSecondText(secondLine.slice(0, index));
          index++;
        } else {
          clearInterval(timer);
          setTimeout(() => setCurrentStep(2), 600); // Pause to read
        }
      }, 60); // Smooth readable speed
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  // Logo animation phase - proper time to see logo
  useEffect(() => {
    if (currentStep === 2) {
      setTimeout(() => setCurrentStep(3), 2000); // Enough time to see logo
    }
  }, [currentStep]);

  // Final transition - smooth exit
  useEffect(() => {
    if (currentStep === 3) {
      setTimeout(() => {
        setIsVisible(false);
        setTimeout(onComplete, 300);
      }, 800); // Smooth transition
    }
  }, [currentStep, onComplete]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden"
        >
          {/* Animated gradient background */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"
          />

          {/* Romantic floating particles */}
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                initial={{
                  opacity: 0,
                  x: Math.random() * 1200,
                  y: Math.random() * 800,
                  scale: 0
                }}
                animate={{
                  opacity: [0, 0.6, 0],
                  y: [null, -100],
                  scale: [0, 1, 0]
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                  ease: "easeInOut"
                }}
                className="absolute w-1 h-1 bg-[#F66581] rounded-full"
              />
            ))}
          </div>

          {/* Ambient glow effect */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 2, ease: "easeOut" }}
            className="absolute inset-0 bg-gradient-radial from-[#F66581]/10 via-[#F66581]/5 to-transparent"
          />

          {/* Main content container */}
          <div className="relative z-10 text-center max-w-lg mx-auto px-6">
            {/* Greeting Phase */}
            <AnimatePresence>
              {currentStep < 2 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30, scale: 0.95 }}
                  transition={{
                    duration: 0.8,
                    ease: [0.25, 0.46, 0.45, 0.94] // Custom smooth easing
                  }}
                >
                  <motion.p
                    className="text-2xl md:text-3xl text-white font-light mb-4 min-h-[2.5rem] tracking-wide"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      delay: 0.2,
                      duration: 0.6,
                      ease: "easeOut"
                    }}
                  >
                    <motion.span
                      initial={{ backgroundSize: "0% 100%" }}
                      animate={{ backgroundSize: "100% 100%" }}
                      transition={{
                        duration: greeting.length * 0.05 + 1,
                        ease: "easeInOut"
                      }}
                      className="bg-gradient-to-r from-white via-pink-100 to-white bg-clip-text text-transparent bg-no-repeat"
                      style={{ backgroundImage: "linear-gradient(90deg, #ffffff 0%, #fce7f3 50%, #ffffff 100%)" }}
                    >
                      {greetingText}
                    </motion.span>
                    {currentStep === 0 && (
                      <motion.span
                        animate={{
                          opacity: [0, 1, 1, 0],
                          scale: [0.8, 1, 1, 0.8]
                        }}
                        transition={{
                          duration: 1.2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="text-[#F66581] ml-1 inline-block"
                      >
                        ❤
                      </motion.span>
                    )}
                  </motion.p>

                  {currentStep >= 1 && (
                    <motion.p
                      initial={{ opacity: 0, y: 20, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{
                        duration: 0.7,
                        ease: [0.25, 0.46, 0.45, 0.94]
                      }}
                      className="text-lg md:text-xl text-gray-300 font-light min-h-[1.5rem] tracking-wide"
                    >
                      <motion.span
                        initial={{ backgroundSize: "0% 100%" }}
                        animate={{ backgroundSize: "100% 100%" }}
                        transition={{
                          duration: secondLine.length * 0.06 + 0.8,
                          ease: "easeInOut"
                        }}
                        className="bg-gradient-to-r from-gray-300 via-pink-200 to-gray-300 bg-clip-text text-transparent bg-no-repeat"
                      >
                        {secondText}
                      </motion.span>
                      {currentStep === 1 && (
                        <motion.span
                          animate={{
                            opacity: [0, 1, 1, 0],
                            rotate: [0, 5, -5, 0]
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          className="text-[#F66581] ml-1 inline-block"
                        >
                          ✨
                        </motion.span>
                      )}
                    </motion.p>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            {/* Logo Animation Phase */}
            <AnimatePresence>
              {currentStep >= 2 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.5, y: 30 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{
                    opacity: 0,
                    scale: 1.1,
                    y: -20,
                    filter: "blur(5px)"
                  }}
                  transition={{
                    duration: 1.8,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    type: "spring",
                    stiffness: 60,
                    damping: 25
                  }}
                >
                  {/* Logo container with elegant reveal */}
                  <div className="relative mb-8">
                    {/* Multi-layered animated glow background */}
                    <motion.div
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.8, delay: 0.1 }}
                      className="absolute inset-0 flex items-center justify-center"
                    >
                      {/* Outer glow ring */}
                      <motion.div
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [0.2, 0.4, 0.2],
                          rotate: [0, 180, 360]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                        className="absolute w-60 h-60 bg-gradient-to-r from-[#F66581]/20 via-[#FF69B4]/30 to-[#F66581]/20 rounded-full blur-3xl"
                      />

                      {/* Inner pulsing glow */}
                      <motion.div
                        animate={{
                          scale: [0.8, 1.1, 0.8],
                          opacity: [0.4, 0.7, 0.4]
                        }}
                        transition={{
                          duration: 2.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="absolute w-40 h-40 bg-gradient-radial from-[#F66581]/40 to-transparent rounded-full blur-2xl"
                      />
                    </motion.div>

                    {/* Logo image with smooth entrance and gradient overlay */}
                    <motion.div
                      initial={{ scale: 0.5, opacity: 0, rotateY: -45 }}
                      animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                      transition={{
                        duration: 1.8,
                        delay: 0.4,
                        type: "spring",
                        stiffness: 80,
                        damping: 20
                      }}
                      className="relative flex items-center justify-center"
                    >
                      {/* Gradient overlay for logo */}
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.5, duration: 0.8 }}
                        className="absolute inset-0 bg-gradient-to-t from-transparent via-[#F66581]/10 to-transparent rounded-lg"
                      />

                      <motion.div
                        className="w-40 h-20 md:w-64 md:h-24 relative"
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <Image
                          src="/logo.png"
                          alt="Urvashi Logo"
                          fill
                          className="object-contain z-50"
                          priority
                          unoptimized
                        />
                      </motion.div>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Skip button */}
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.7 }}
            whileHover={{ opacity: 1, scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ delay: 3, duration: 0.3 }}
            onClick={() => {
              setIsVisible(false);
              setTimeout(onComplete, 100);
            }}
            className="absolute top-6 right-6 z-20 px-4 py-2 text-sm text-gray-400 hover:text-white border border-gray-600 hover:border-[#F66581] rounded-full backdrop-blur-sm bg-black/20 hover:bg-[#F66581]/10 transition-all duration-300"
          >
            Skip
          </motion.button>

          {/* Final transition overlay */}
          <AnimatePresence>
            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-black"
              />
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
