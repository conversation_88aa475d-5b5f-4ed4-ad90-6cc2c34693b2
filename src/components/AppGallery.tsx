'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { useState } from 'react';

interface AppGalleryProps {
  className?: string;
}

export default function AppGallery({ className = "" }: AppGalleryProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const appImages = [
    {
      src: '/app/1.webp',
      alt: 'Chat with Urvashi',
      rotation: -8,
      delay: 0.2,
      zIndex: 3,
    },
    {
      src: '/app/2.webp', 
      alt: 'Choose AI Girlfriend',
      rotation: 5,
      delay: 0.4,
      zIndex: 2,
    },
    {
      src: '/app/3.webp',
      alt: 'App Features',
      rotation: -3,
      delay: 0.6,
      zIndex: 1,
    },
  ];

  return (
    <div id='app-gallery' className={`relative w-full h-full flex items-center justify-center ${className}`}>
      {/* Background glow effect */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1.2, delay: 0.5 }}
        className="absolute inset-0 bg-gradient-radial from-pink-500/20 via-purple-500/10 to-transparent rounded-full blur-3xl"
      />

      {/* App Screenshots */}
      <div className="relative w-80 h-96">
        {appImages.map((image, index) => (
          <motion.div
            key={index}
            initial={{ 
              opacity: 0, 
              y: 50, 
              rotate: 0,
              scale: 0.8 
            }}
            animate={{ 
              opacity: 1, 
              y: 0, 
              rotate: image.rotation,
              scale: 1 
            }}
            transition={{
              duration: 0.8,
              delay: image.delay,
              type: "spring",
              stiffness: 100,
              damping: 15
            }}
            whileHover={{
              scale: 1.05,
              rotate: 0,
              zIndex: 10,
              transition: { duration: 0.3 }
            }}
            onHoverStart={() => setHoveredIndex(index)}
            onHoverEnd={() => setHoveredIndex(null)}
            className="absolute cursor-pointer"
            style={{
              left: `${index * 60}px`,
              top: `${index * 20}px`,
              zIndex: hoveredIndex === index ? 10 : image.zIndex,
            }}
          >
            {/* Glow effect */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.6 }}
              transition={{ duration: 1, delay: image.delay + 0.3 }}
              className="absolute -inset-4 bg-gradient-to-r from-pink-500/30 to-purple-500/30 rounded-3xl blur-xl"
            />
            
            {/* Phone mockup */}
            <motion.div
              animate={{
                y: [0, -10, 0],
              }}
              transition={{
                duration: 3 + index * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: image.delay
              }}
              className="relative bg-black rounded-3xl p-2 shadow-2xl border border-white/10"
            >
              <div className="relative w-48 h-96 bg-gradient-to-br from-gray-900 to-black rounded-2xl overflow-hidden">
                {/* Screen content */}
                <Image
                  src={image.src}
                  alt={image.alt}
                  width={192}
                  height={384}
                  className="w-full h-full object-cover rounded-2xl"
                />
                
                {/* Screen overlay for realism */}
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5 rounded-2xl" />
              </div>
              
              {/* Phone details */}
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-white/20 rounded-full" />
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white/20 rounded-full" />
            </motion.div>

            {/* Floating particles around hovered image */}
            {hoveredIndex === index && (
              <>
                {[...Array(6)].map((_, particleIndex) => (
                  <motion.div
                    key={particleIndex}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ 
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      x: [0, (Math.random() - 0.5) * 100],
                      y: [0, (Math.random() - 0.5) * 100],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: particleIndex * 0.2,
                    }}
                    className="absolute w-2 h-2 bg-pink-400 rounded-full"
                    style={{
                      left: '50%',
                      top: '50%',
                    }}
                  />
                ))}
              </>
            )}
          </motion.div>
        ))}
      </div>

      {/* Ambient sparkles */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0 }}
          animate={{ 
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: Math.random() * 3,
          }}
          className="absolute w-1 h-1 bg-pink-400 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
        />
      ))}
    </div>
  );
}
