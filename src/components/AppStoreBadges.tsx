'use client';

import { Smartphone, Download } from 'lucide-react';

interface AppStoreBadgesProps {
  variant?: 'default' | 'large';
  className?: string;
}

export default function AppStoreBadges({ variant = 'default', className = '' }: AppStoreBadgesProps) {
  const isLarge = variant === 'large';

  return (
    <div className={`flex flex-col sm:flex-row gap-4 ${className}`}>
      {/* Google Play Store Badge */}
      <a
        href="#"
        className={`group flex items-center gap-3 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white ${isLarge ? 'px-6 py-4' : 'px-4 py-3'
          } rounded-xl transition-all duration-300 shadow-lg hover:shadow-pink-500/25 hover:scale-105 min-w-[200px]`}
      >
        <div className={`${isLarge ? 'p-2' : 'p-1'} bg-white/20 rounded-lg`}>
          <Smartphone size={isLarge ? 24 : 20} />
        </div>
        <div className="text-left">
          <div className={`${isLarge ? 'text-xs' : 'text-[10px]'} text-white/80 uppercase tracking-wide`}>
            Get it on
          </div>
          <div className={`${isLarge ? 'text-lg' : 'text-sm'} font-semibold leading-tight`}>
            Google Play
          </div>
        </div>
      </a>

      {/* Apple App Store Badge */}
      <a
        href="#"
        className={`group flex items-center gap-3 bg-black hover:bg-gray-900 text-white ${isLarge ? 'px-6 py-4' : 'px-4 py-3'
          } rounded-xl transition-all duration-300 shadow-lg hover:shadow-gray-500/25 hover:scale-105 border border-white/20 hover:border-white/30 min-w-[200px]`}
      >
        <div className={`${isLarge ? 'p-2' : 'p-1'} bg-white/10 rounded-lg`}>
          <Download size={isLarge ? 24 : 20} />
        </div>
        <div className="text-left">
          <div className={`${isLarge ? 'text-xs' : 'text-[10px]'} text-white/80 uppercase tracking-wide`}>
            Download on the
          </div>
          <div className={`${isLarge ? 'text-lg' : 'text-sm'} font-semibold leading-tight`}>
            App Store
          </div>
        </div>
      </a>
    </div>
  );
}

// Alternative compact version for navigation
export function CompactAppStoreBadges({ className = '' }: { className?: string }) {
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Google Play Store */}
      <a
        target='_blank'
        href="https://play.google.com/store/apps/details?id=urvashi.indian.ai.girlfriend.gf.virtual.dating.lover"
        className="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-3 py-2 rounded-full transition-all duration-300 shadow-lg hover:shadow-pink-500/25 hover:scale-105"
      >
        <svg
          className="w-5 h-5 text-white group-hover:text-pink-400 transition-colors duration-300"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
        </svg>
        <span className="text-xs font-medium hidden sm:inline">Play Store</span>
      </a>

      {/* Apple App Store */}
      <a
        target='_blank'
        href="https://apps.apple.com/us/app/urvashi-indian-ai-girlfriend/id6745728476"
        className="flex items-center gap-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-3 py-2 rounded-full transition-all duration-300 border border-white/20 hover:border-white/30 hover:scale-105"
      >
        <svg
          className="w-5 h-5 text-white group-hover:text-pink-400 transition-colors duration-300"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
        </svg>
        <span className="text-xs font-medium hidden sm:inline">App Store</span>
      </a>
    </div>
  );
}
