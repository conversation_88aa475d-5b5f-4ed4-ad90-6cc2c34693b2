'use client';

import { motion } from 'framer-motion';

interface AppStoreButtonsProps {
  className?: string;
  variant?: 'default' | 'large';
}

export default function AppStoreButtons({
  className = "",
  variant = 'default'
}: AppStoreButtonsProps) {
  const buttonSize = variant === 'large' ? 'h-14' : 'h-12';
  const textSize = variant === 'large' ? 'text-sm' : 'text-xs';

  const buttons = [
    {
      type: 'apple',
      title: 'Download on the',
      subtitle: 'App Store',
      href: 'https://apps.apple.com/us/app/urvashi-indian-ai-girlfriend/id6745728476',
      delay: 0.2,
    },
    {
      type: 'google',
      title: 'Get it on',
      subtitle: 'Google Play',
      href: 'https://play.google.com/store/apps/details?id=urvashi.indian.ai.girlfriend.gf.virtual.dating.lover',
      delay: 0.4,
    },
  ];

  return (
    <div className={`flex flex-col sm:flex-row gap-4 items-center ${className}`}>
      {buttons.map((button) => (
        <motion.a
          target='_blank'
          key={button.type}
          href={button.href}
          initial={{ opacity: 0, y: 20, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{
            duration: 0.6,
            delay: button.delay,
            type: "spring",
            stiffness: 200,
            damping: 15
          }}
          whileHover={{
            scale: 1.05,
            y: -2,
            transition: { duration: 0.2 }
          }}
          whileTap={{
            scale: 0.95,
            transition: { duration: 0.1 }
          }}
          className={`
            inline-flex items-center gap-3 px-6 py-3 ${buttonSize}
            bg-black border border-white/20 rounded-xl
            hover:bg-white/5 hover:border-white/30
            transition-all duration-300 group
            shadow-lg hover:shadow-xl
            backdrop-blur-sm
          `}
        >
          {/* App Store Icon */}
          {button.type === 'apple' && (
            <motion.div
              initial={{ rotate: 0 }}
              whileHover={{ rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <svg
                className="w-8 h-8 text-white group-hover:text-pink-400 transition-colors duration-300"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
              </svg>
            </motion.div>
          )}

          {/* Google Play Icon */}
          {button.type === 'google' && (
            <motion.div
              initial={{ rotate: 0 }}
              whileHover={{ rotate: -5 }}
              transition={{ duration: 0.2 }}
            >
              <svg
                className="w-8 h-8 text-white group-hover:text-pink-400 transition-colors duration-300"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
              </svg>
            </motion.div>
          )}

          {/* Button Text */}
          <div className="flex flex-col items-start">
            <motion.span
              className={`${textSize} text-white/70 leading-none group-hover:text-white/90 transition-colors duration-300`}
              initial={{ opacity: 0.7 }}
              whileHover={{ opacity: 1 }}
            >
              {button.title}
            </motion.span>
            <motion.span
              className={`${variant === 'large' ? 'text-lg' : 'text-base'} font-semibold text-white leading-none group-hover:text-pink-400 transition-colors duration-300`}
              initial={{ y: 0 }}
              whileHover={{ y: -1 }}
              transition={{ duration: 0.2 }}
            >
              {button.subtitle}
            </motion.span>
          </div>

          {/* Hover glow effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"
            initial={{ scale: 0.8 }}
            whileHover={{ scale: 1 }}
            transition={{ duration: 0.3 }}
          />
        </motion.a>
      ))}
    </div>
  );
}
