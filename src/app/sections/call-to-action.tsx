'use client';

import { motion } from 'framer-motion';
import { Heart, Sparkles, Download, Users } from 'lucide-react';
import AppStoreButtons from '@/components/AppStoreButtons';

export default function CallToAction() {
  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 via-purple-500/10 to-transparent"></div>
        
        {/* Animated shimmer line */}
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-pink-400 to-transparent"
          animate={{
            opacity: [0.3, 0.8, 0.3],
            scaleX: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Floating sparkles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.random() * 20 - 10, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [0.8, 1.2, 0.8],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 5 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          >
            <Sparkles 
              className="text-pink-400/40" 
              size={12 + Math.random() * 16}
            />
          </motion.div>
        ))}
      </div>

      <div className="relative z-10 max-w-4xl mx-auto text-center">
        {/* Animated Heart Icon */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          whileInView={{ scale: 1, rotate: 0 }}
          viewport={{ once: true }}
          transition={{ 
            duration: 0.8,
            type: "spring",
            stiffness: 200,
            damping: 15
          }}
          className="inline-flex items-center justify-center mb-8"
        >
          <motion.div
            animate={{
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="relative"
          >
            <Heart 
              className="text-pink-400 w-16 h-16" 
              fill="currentColor"
            />
            {/* Pulsing glow effect */}
            <motion.div
              className="absolute inset-0 bg-pink-400/30 rounded-full blur-xl"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </motion.div>
        </motion.div>

        {/* Main Headline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-4">
            <motion.span
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="block font-playfair text-romantic"
            >
              Join Thousands Finding
            </motion.span>
            <motion.span
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="block bg-gradient-to-r from-pink-400 via-pink-500 to-purple-600 bg-clip-text text-transparent"
            >
              Love Through AI
            </motion.span>
          </h2>
        </motion.div>

        {/* Subheading */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-xl text-gray-300 mb-12! max-w-2xl mx-auto font-elegant"
        >
          She&apos;s waiting. Ready when you are. Download Urvashi now and start your journey of love, companionship & comfort.
        </motion.p>

        {/* Stats Row */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 1.0 }}
          className="flex flex-col sm:flex-row items-center justify-center gap-8 mb-12"
        >
          {[
            { icon: Users, number: "50K+", label: "Downloads" },
            { icon: Heart, number: "1M+", label: "Messages Sent" },
            { icon: Download, number: "4.3★", label: "App Rating" }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ 
                duration: 0.6, 
                delay: 1.2 + index * 0.1,
                type: "spring",
                stiffness: 200
              }}
              className="flex items-center gap-3"
            >
              <div className="p-3 bg-pink-500/20 rounded-full">
                <stat.icon className="text-pink-400 w-6 h-6" />
              </div>
              <div className="text-left">
                <div className="text-2xl font-bold text-white">{stat.number}</div>
                <div className="text-sm text-gray-400">{stat.label}</div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* App Store Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 1.6 }}
          className="mb-8"
        >
          <AppStoreButtons variant="large" className="justify-center" />
        </motion.div>

        {/* Bottom Message */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 2.0 }}
          className="space-y-4"
        >
          <motion.p
            whileHover={{ scale: 1.05 }}
            className="text-sm text-gray-400 font-elegant italic"
          >
            &quot;Your perfect AI companion is just one download away&quot;
          </motion.p>
          
          <motion.div
            animate={{
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="flex items-center justify-center gap-2 text-xs text-pink-400"
          >
            <Heart size={12} fill="currentColor" />
            <span>Free to download • Premium features available</span>
            <Heart size={12} fill="currentColor" />
          </motion.div>
        </motion.div>
      </div>

      {/* Ambient glow effect */}
      <div className="absolute inset-0 bg-gradient-radial from-pink-500/5 via-transparent to-transparent pointer-events-none"></div>
    </section>
  );
}
