'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { useState } from 'react';
import { <PERSON>, Sparkles, Star } from 'lucide-react';

export default function AppScreenshots() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // App screenshots data
  const screenshots = [
    {
      src: '/app/1.webp',
      alt: 'Chat with Urvashi - Intimate conversations',
      title: 'Intimate Conversations',
      description: 'Deep, meaningful chats that feel real'
    },
    {
      src: '/app/2.webp',
      alt: 'Choose your AI girlfriend',
      title: 'Choose Your Perfect Match',
      description: 'Multiple personalities to connect with'
    },
    {
      src: '/app/3.webp',
      alt: 'App features and customization',
      title: 'Personalized Experience',
      description: 'Tailored just for you'
    },
    {
      src: '/app/4.webp',
      alt: 'Explore stunning AI girlfriend visuals',
      title: '100s of Gorgeous AI Companions',
      description: 'Explore a gallery of breathtaking, lifelike AI girlfriends'
    },
    {
      src: '/app/5.webp',
      alt: 'Customize your girlfriend',
      title: 'Customize Her Personality',
      description: 'Shape her mood, tone, and traits to match your vibe'
    },
    {
      src: '/app/6.webp',
      alt: 'Multilingual AI girlfriend support',
      title: 'Speaks Your Language',
      description: 'Supports 10+ languages including Hindi, Tamil, Bengali & more'
    }
  ];

  // Floating hearts animation
  const FloatingHearts = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            opacity: 0,
            y: 100,
            x: Math.random() * window.innerWidth,
            scale: 0
          }}
          animate={{
            opacity: [0, 0.6, 0],
            y: -100,
            scale: [0, 1, 0.8]
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            delay: Math.random() * 10,
            ease: "easeOut"
          }}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
          }}
        >
          <Heart
            className="text-pink-500/20"
            size={12 + Math.random() * 16}
            fill="currentColor"
          />
        </motion.div>
      ))}
    </div>
  );

  // Sparkles animation
  const FloatingSparkles = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(12)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            opacity: 0,
            scale: 0,
            rotate: 0
          }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
            rotate: 360
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            repeat: Infinity,
            delay: Math.random() * 5,
            ease: "easeInOut"
          }}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
        >
          <Sparkles
            className="text-purple-400/30"
            size={8 + Math.random() * 12}
          />
        </motion.div>
      ))}
    </div>
  );

  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-transparent to-purple-500/5" />

        {/* Floating animations */}
        <FloatingHearts />
        <FloatingSparkles />

        {/* Radial gradient overlay */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/10 via-transparent to-transparent opacity-50" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 mb-4"
          >
            <Star className="text-pink-500" size={20} fill="currentColor" />
            <span className="text-pink-400 font-medium text-sm uppercase tracking-wider">
              App Gallery
            </span>
            <Star className="text-pink-500" size={20} fill="currentColor" />
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-4xl md:text-5xl lg:text-6xl font-romantic! text-romantic mb-6"
          >
            A glimpse into your moments with Urvashi
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto font-elegant"
          >
            Experience her world — one screen at a time. Every interaction is a memory waiting to be made.
          </motion.p>
        </motion.div>

        {/* Screenshots Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12"
        >
          {screenshots.map((screenshot, index) => (
            <motion.div
              key={index}
              initial={{
                opacity: 0,
                y: 60,
                scale: 0.8
              }}
              whileInView={{
                opacity: 1,
                y: 0,
                scale: 1
              }}
              viewport={{ once: true }}
              transition={{
                duration: 0.8,
                delay: 0.6 + index * 0.1,
                type: "spring",
                stiffness: 100,
                damping: 15
              }}
              whileHover={{
                scale: 1.05,
                y: -10,
                transition: { duration: 0.3 }
              }}
              onHoverStart={() => setHoveredIndex(index)}
              onHoverEnd={() => setHoveredIndex(null)}
              className="group cursor-pointer"
            >
              {/* Glass container */}
              <div className="relative glass-effect-strong rounded-3xl p-6 hover:bg-white/10 transition-all duration-500">
                {/* Glow effect on hover */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: hoveredIndex === index ? 1 : 0,
                  }}
                  transition={{ duration: 0.3 }}
                  className="absolute inset-0 rounded-3xl bg-gradient-to-br from-pink-500/20 to-purple-500/20 blur-xl"
                />

                {/* Phone mockup */}
                <div className="relative z-10">
                  <motion.div
                    animate={{
                      y: hoveredIndex === index ? [-5, 5, -5] : [0, -8, 0],
                    }}
                    transition={{
                      duration: hoveredIndex === index ? 2 : 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="relative mx-auto w-48 h-96 bg-gradient-to-br from-gray-900 to-black rounded-3xl p-3 shadow-2xl border border-white/20"
                  >
                    {/* Screen */}
                    <div className="relative w-full h-full rounded-2xl overflow-hidden bg-black">
                      <Image
                        src={screenshot.src}
                        alt={screenshot.alt}
                        width={192}
                        height={384}
                        className="w-full h-full object-cover"
                      />

                      {/* Screen overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5" />

                      {/* Romantic shimmer effect */}
                      <motion.div
                        initial={{ x: '-100%' }}
                        animate={{ x: hoveredIndex === index ? '100%' : '-100%' }}
                        transition={{ duration: 1.5, ease: "easeInOut" }}
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-pink-500/20 to-transparent"
                      />
                    </div>
                  </motion.div>

                  {/* Content */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                    className="text-center mt-6"
                  >
                    <h3 className="text-xl font-elegant text-white mb-2 group-hover:text-pink-300 transition-colors duration-300">
                      {screenshot.title}
                    </h3>
                    <p className="text-gray-400 text-sm font-professional group-hover:text-gray-300 transition-colors duration-300">
                      {screenshot.description}
                    </p>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="text-center mt-16"
        >
          <motion.p
            whileHover={{ scale: 1.05 }}
            className="text-lg text-gray-300 font-elegant italic"
          >
            &quot;Every screenshot tells a story of love, companionship, and connection&quot;
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}
