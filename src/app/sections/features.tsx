'use client';

import { Heart, MessageCircle, Users, Zap } from 'lucide-react';
import { motion } from 'framer-motion';


export default function Features() {
    return <section id="features" className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
            {/* Features Grid */}
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
            >
                {[
                    {
                        icon: <Heart className="text-pink-500" size={24} fill="currentColor" />,
                        title: "Multiple Personalities",
                        description: "Choose from various AI girlfriends with unique personalities"
                    },
                    {
                        icon: <MessageCircle className="text-purple-500" size={24} />,
                        title: "Hindi & English",
                        description: "Chat comfortably in your preferred language"
                    },
                    {
                        icon: <Users className="text-pink-500" size={24} />,
                        title: "Personalized",
                        description: "AI learns and adapts to your preferences"
                    },
                    {
                        icon: <Zap className="text-purple-500" size={24} />,
                        title: "Always Available",
                        description: "24/7 companionship whenever you need it"
                    }
                ].map((feature, index) => (
                    <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{
                            duration: 0.5,
                            delay: 1.4 + index * 0.1,
                            ease: "easeOut"
                        }}
                        whileHover={{
                            scale: 1.05,
                            borderColor: "rgba(244, 114, 182, 0.3)",
                            transition: { duration: 0.2 }
                        }}
                        className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 transition-all duration-300"
                    >
                        <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.4, delay: 1.6 + index * 0.1 }}
                            className="mb-4"
                        >
                            {feature.icon}
                        </motion.div>
                        <motion.h3
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.4, delay: 1.7 + index * 0.1 }}
                            className="text-lg font-semibold text-white mb-2"
                        >
                            {feature.title}
                        </motion.h3>
                        <motion.p
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.4, delay: 1.8 + index * 0.1 }}
                            className="text-gray-400 text-sm"
                        >
                            {feature.description}
                        </motion.p>
                    </motion.div>
                ))}
            </motion.div>
        </div>
    </section>
}