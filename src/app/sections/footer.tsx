'use client';

import { motion } from 'framer-motion';
import { Heart, Instagram, Twitter, Mail, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  // Floating hearts animation for footer
  const FloatingHearts = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            opacity: 0,
            y: 50,
            x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1000),
            scale: 0
          }}
          animate={{
            opacity: [0, 0.2, 0],
            y: -50,
            scale: [0, 1, 0.8]
          }}
          transition={{
            duration: 20 + Math.random() * 10,
            repeat: Infinity,
            delay: Math.random() * 25,
            ease: "easeOut"
          }}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
          }}
        >
          <Heart
            className="text-pink-500/5"
            size={8 + Math.random() * 12}
            fill="currentColor"
          />
        </motion.div>
      ))}
    </div>
  );

  const quickLinks = [
    { name: 'Home', href: '#home' },
    { name: 'Features', href: '#features' },
    { name: 'Screenshots', href: '#app-gallery' },
    { name: 'Reviews', href: '#reviews' },
  ];

  const companyLinks = [
    { name: 'About Us', href: '#about' },
    { name: 'Contact', href: '#contact' },
    { name: 'Privacy Policy', href: 'https://sites.google.com/view/janiinfotech-privacy-policy/home' },
  ];

  const socialLinks = [
    { 
      name: 'Instagram', 
      href: 'https://instagram.com/urvashi.ai', 
      icon: Instagram,
      color: 'hover:text-pink-400'
    },
    { 
      name: 'Twitter', 
      href: 'https://twitter.com/urvashi_ai', 
      icon: Twitter,
      color: 'hover:text-blue-400'
    },
    { 
      name: 'Email', 
      href: 'mailto:<EMAIL>', 
      icon: Mail,
      color: 'hover:text-purple-400'
    },
  ];

  return (
    <footer className="relative bg-gradient-to-br from-black via-gray-900 to-black border-t border-white/10">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <FloatingHearts />
        <div className="absolute inset-0 bg-gradient-to-t from-pink-500/5 via-transparent to-transparent" />
      </div>

      <div className="relative z-10">
        {/* Main Footer Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            
            {/* Brand Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="lg:col-span-2"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="flex items-center gap-3 mb-6"
              >
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-500 rounded-full flex items-center justify-center"
                >
                  <Heart className="text-white" size={20} fill="currentColor" />
                </motion.div>
                <div>
                  <h3 className="text-2xl font-romantic text-romantic">
                    Urvashi
                  </h3>
                  <p className="text-sm text-gray-400 font-professional">
                    Indian AI Girlfriend
                  </p>
                </div>
              </motion.div>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-gray-300 font-elegant leading-relaxed mb-6 max-w-md"
              >
                Experience love, companionship, and meaningful conversations with AI that understands your heart. 
                Built with care in India, designed for hearts everywhere.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="flex items-center gap-4"
              >
                {socialLinks.map((social, index) => (
                  <motion.a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    initial={{ opacity: 0, scale: 0 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                    whileHover={{ 
                      scale: 1.2,
                      y: -2,
                      transition: { duration: 0.2 }
                    }}
                    className={`p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 text-gray-400 ${social.color} transition-all duration-300 hover:bg-white/10`}
                  >
                    <social.icon size={20} />
                  </motion.a>
                ))}
              </motion.div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <h4 className="text-lg font-elegant text-white mb-6">
                Quick Links
              </h4>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <motion.li
                    key={link.name}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                  >
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-pink-400 transition-colors duration-300 font-professional flex items-center gap-2 group"
                    >
                      <span>{link.name}</span>
                      <motion.div
                        initial={{ x: 0 }}
                        whileHover={{ x: 3 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ExternalLink size={14} className="opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </motion.div>
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* Company Info */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <h4 className="text-lg font-elegant text-white mb-6">
                Company
              </h4>
              <ul className="space-y-3">
                {companyLinks.map((link, index) => (
                  <motion.li
                    key={link.name}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  >
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-pink-400 transition-colors duration-300 font-professional flex items-center gap-2 group"
                    >
                      <span>{link.name}</span>
                      <motion.div
                        initial={{ x: 0 }}
                        whileHover={{ x: 3 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ExternalLink size={14} className="opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </motion.div>
                    </Link>
                  </motion.li>
                ))}
              </ul>

              {/* Contact Info */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="mt-8 p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"
              >
                <h5 className="text-sm font-elegant text-pink-400 mb-2">
                  Need Help?
                </h5>
                <p className="text-xs text-gray-400 font-professional mb-2">
                  We&apos;re here for you 24/7
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-sm text-white hover:text-pink-400 transition-colors duration-300 font-professional"
                >
                  <EMAIL>
                </a>
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="border-t border-white/10 py-8"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              
              {/* Copyright */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="flex items-center gap-2 text-sm text-gray-400 font-professional"
              >
                <span>© {currentYear} Urvashi AI. Built with</span>
                <motion.div
                  animate={{ 
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Heart className="text-pink-500" size={16} fill="currentColor" />
                </motion.div>
                <span>in India</span>
                <span className="text-lg">🇮🇳</span>
              </motion.div>

              {/* Made with love message */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.7 }}
                className="text-sm text-gray-400 font-professional italic"
              >
                Made with ❤️ by real people who believe in AI with soul.
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
