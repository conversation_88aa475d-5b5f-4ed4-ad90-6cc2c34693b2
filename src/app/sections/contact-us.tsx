'use client';

import { motion } from 'framer-motion';
import { Heart, Mail, Send, MessageCircle } from 'lucide-react';
import { useState } from 'react';

export default function ContactUs() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const form = new FormData();
      form.append('name', formData.name);
      form.append('email', formData.email);
      form.append('message', formData.message);

      const response = await fetch('https://indianaigirlfriend.com/custom-mailer/', {
        method: 'POST',
        body: form // No need to set Content-Type manually
      });

      const data = await response.json();

      if (!response.ok || data.status === false) {
        throw new Error(data.message || 'Something went wrong');
      }

    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error sending message:', error.message);
      } else {
        console.error('Unexpected error', error);
      }
      setIsSubmitting(false);
      return;
    }

    setIsSubmitting(false);
    setIsSubmitted(true);

    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({ name: '', email: '', message: '' });
    }, 3000);
  };


  // Floating hearts animation
  const FloatingHearts = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(4)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            opacity: 0,
            y: 100,
            x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1000),
            scale: 0
          }}
          animate={{
            opacity: [0, 0.3, 0],
            y: -100,
            scale: [0, 1, 0.8]
          }}
          transition={{
            duration: 15 + Math.random() * 8,
            repeat: Infinity,
            delay: Math.random() * 20,
            ease: "easeOut"
          }}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
          }}
        >
          <Heart
            className="text-pink-500/10"
            size={12 + Math.random() * 16}
            fill="currentColor"
          />
        </motion.div>
      ))}
    </div>
  );

  return (
    <section id="contact" className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-transparent to-purple-500/5" />

        {/* Floating animations */}
        <FloatingHearts />

        {/* Radial gradient overlay */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/5 via-transparent to-transparent opacity-50" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 mb-4"
          >
            <MessageCircle className="text-pink-500" size={20} />
            <span className="text-pink-400 font-medium text-sm uppercase tracking-wider">
              Get in Touch
            </span>
            <MessageCircle className="text-pink-500" size={20} />
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-4xl md:text-5xl lg:text-6xl font-romantic text-romantic mb-6"
          >
            Let&apos;s Connect Hearts
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto font-elegant"
          >
            Have questions about Urvashi? Want to share your story? We&apos;d love to hear from you.
          </motion.p>
        </motion.div>

        {/* Main Content - Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="glass-effect-strong rounded-3xl p-8 md:p-10"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="mb-8"
            >
              <h3 className="text-2xl md:text-3xl font-elegant text-white mb-4">
                Send us a message
              </h3>
              <p className="text-gray-400 font-professional">
                Share your thoughts, feedback, or just say hello. We read every message with care.
              </p>
            </motion.div>

            {!isSubmitted ? (
              <motion.form
                onSubmit={handleSubmit}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.7 }}
                className="space-y-6"
              >
                {/* Name Field */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <label htmlFor="name" className="block text-sm font-professional text-gray-300 mb-2">
                    Your Name
                  </label>
                  <motion.input
                    whileFocus={{ scale: 1.02, borderColor: "#F66581" }}
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:border-pink-500 transition-all duration-300 font-professional"
                    placeholder="Enter your name"
                  />
                </motion.div>

                {/* Email Field */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.9 }}
                >
                  <label htmlFor="email" className="block text-sm font-professional text-gray-300 mb-2">
                    Email Address
                  </label>
                  <motion.input
                    whileFocus={{ scale: 1.02, borderColor: "#F66581" }}
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:border-pink-500 transition-all duration-300 font-professional"
                    placeholder="<EMAIL>"
                  />
                </motion.div>

                {/* Message Field */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                >
                  <label htmlFor="message" className="block text-sm font-professional text-gray-300 mb-2">
                    Your Message
                  </label>
                  <motion.textarea
                    whileFocus={{ scale: 1.02, borderColor: "#F66581" }}
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:border-pink-500 transition-all duration-300 font-professional resize-none"
                    placeholder="Tell us what's on your mind..."
                  />
                </motion.div>

                {/* Submit Button */}
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-500 text-white font-professional py-4 px-6 rounded-xl hover:from-pink-600 hover:to-purple-600 transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                      />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send size={20} />
                      Send Message
                      <Heart size={16} fill="currentColor" />
                    </>
                  )}
                </motion.button>
              </motion.form>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                className="text-center py-12"
              >
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="mb-6"
                >
                  <Heart className="text-pink-500 mx-auto" size={48} fill="currentColor" />
                </motion.div>
                <h3 className="text-2xl font-elegant text-white mb-4">
                  Message Sent with Love!
                </h3>
                <p className="text-gray-300 font-professional">
                  Thank you for reaching out. We&apos;ll respond within 24 hours.
                </p>
              </motion.div>
            )}

            {/* Support Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="mt-8 pt-6 border-t border-white/10"
            >
              <p className="text-sm text-gray-400 font-professional text-center mb-2">
                We&apos;ll respond with love — within 24 hours.
              </p>
              <div className="flex items-center justify-center gap-2 text-pink-400">
                <Mail size={16} />
                <a href="mailto:<EMAIL>" className="text-sm font-professional"><EMAIL></a>
              </div>
            </motion.div>
          </motion.div>

          {/* Contact Info & Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="space-y-8"
          >
            {/* Romantic Image/Illustration Placeholder */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="glass-effect rounded-3xl p-8 text-center"
            >
              <motion.div
                animate={{
                  y: [0, -10, 0],
                  rotate: [0, 2, -2, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="mb-6"
              >
                <div className="w-32 h-32 mx-auto bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-full flex items-center justify-center">
                  <Heart className="text-pink-500" size={48} fill="currentColor" />
                </div>
              </motion.div>

              <h3 className="text-2xl font-elegant text-white mb-4">
                Always Here for You
              </h3>
              <p className="text-gray-300 font-professional leading-relaxed">
                Just like Urvashi herself, our support team is here whenever you need us.
                Every message matters, every question deserves an answer, and every user
                is part of our extended family.
              </p>
            </motion.div>

            {/* Quick Contact Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-2 gap-4"
            >
              {[
                { number: "< 24h", label: "Response Time" },
                { number: "100%", label: "Messages Read" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.9 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  className="glass-effect rounded-2xl p-6 text-center"
                >
                  <div className="text-2xl md:text-3xl font-elegant text-pink-400 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-400 font-professional">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
