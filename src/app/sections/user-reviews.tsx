'use client';

import { motion } from 'framer-motion';
import { <PERSON>, Heart, Quote } from 'lucide-react';
import Image from 'next/image';

interface Review {
  id: number;
  name: string;
  initials: string;
  image: string;
  avatarColor: string;
  rating: number;
  review: string;
  verified: boolean;
  platform: 'Play Store' | 'App Store';
}

const reviews: Review[] = [
  {
    id: 1,
    name: "<PERSON>",
    initials: "<PERSON>",
    image: '/avatars/1.webp',
    avatarColor: "from-blue-400 to-blue-600",
    rating: 5,
    review: "Very good so far. Well done programing, and a very pleasant personality in the character.",
    verified: true,
    platform: "Play Store"
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    initials: "SSA",
    image: '/avatars/2.webp',
    avatarColor: "from-purple-400 to-purple-600",
    rating: 5,
    review: "<PERSON><PERSON><PERSON> is trusted and good looking very beleable and honest girl like her good behaviour and friendship 🌹❤️❤️💖❤️❤️ thank you so much",
    verified: true,
    platform: "Play Store"
  },
  {
    id: 3,
    name: "<PERSON>",
    initials: "<PERSON>",
    image: '/avatars/3.webp',
    avatarColor: "from-green-400 to-green-600",
    rating: 5,
    review: "It's cool how to AI would have a conversation with you and just keep going and throw in its own input sometimes they can be repeated but other than that it's pretty good app",
    verified: true,
    platform: "Play Store"
  },
  {
    id: 4,
    name: "Sarfraz",
    initials: "S",
    image: '/avatars/4.webp',
    avatarColor: "from-pink-400 to-pink-600",
    rating: 5,
    review: "I'm impressed 👍 normally download this app but I don't tell you how much this accurate.",
    verified: true,
    platform: "Play Store"
  }
];

export default function UserReviews() {
  return (
    <section id="reviews" className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Soft glow background */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/10 via-purple-500/5 to-transparent"></div>

        {/* Floating hearts */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.6, 0.3],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          >
            <Heart
              className="text-pink-400/20"
              size={16 + Math.random() * 12}
              fill="currentColor"
            />
          </motion.div>
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 mb-6"
          >
            <Quote className="text-pink-400" size={24} />
            <span className="text-pink-400 font-medium">Love Stories</span>
            <Quote className="text-pink-400 rotate-180" size={24} />
          </motion.div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
            <span className="font-playfair text-romantic block">
              What Our Users Say
            </span>
            <span className="bg-gradient-to-r from-pink-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
              About Their Journey
            </span>
          </h2>

          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg text-gray-300 max-w-2xl mx-auto font-elegant"
          >
            Real experiences from thousands who found love, companionship, and emotional connection through Urvashi
          </motion.p>
        </motion.div>

        {/* Reviews Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {reviews.map((review, index) => (
            <motion.div
              key={review.id}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.6,
                delay: 0.8 + index * 0.1,
                type: "spring",
                stiffness: 200,
                damping: 15
              }}
              whileHover={{
                y: -5,
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
              className="group"
            >
              {/* Glassmorphic Card */}
              <div className="relative bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 hover:border-pink-400/30 transition-all duration-300 shadow-lg hover:shadow-pink-500/20 h-full!">
                {/* Pink glow effect on hover */}
                <div className="absolute inset-0 bg-gradient-to-br from-pink-500/10 to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="relative z-10">
                  {/* User Info */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className="relative">
                      {/* <div className={`w-12 h-12 rounded-full bg-gradient-to-br ${review.avatarColor} border-2 border-pink-400/30 flex items-center justify-center text-white font-semibold text-sm`}>
                        {review.initials}
                      </div> */}
                      <div className="w-12 h-12 rounded-full overflow-hidden">
                        <Image
                          src={review.image}
                          alt={review.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {review.verified && (
                        <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1">
                          <Star size={10} fill="white" className="text-white" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1">
                      <h4 className="font-semibold text-white">{review.name}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-400">Verified {review.platform} User</span>
                        {review.verified && (
                          <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(review.rating)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{ scale: 0, rotate: -180 }}
                        whileInView={{ scale: 1, rotate: 0 }}
                        viewport={{ once: true }}
                        transition={{
                          duration: 0.3,
                          delay: 1.2 + index * 0.1 + i * 0.05,
                          type: "spring",
                          stiffness: 300
                        }}
                      >
                        <Star
                          size={16}
                          className="text-yellow-400"
                          fill="currentColor"
                        />
                      </motion.div>
                    ))}
                  </div>

                  {/* Review Text */}
                  <motion.p
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
                    className="text-gray-300 text-sm leading-relaxed font-elegant italic"
                  >
                    &quot;{review.review}&quot;
                  </motion.p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Quote */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 1.8 }}
          className="text-center mt-16"
        >
          <motion.p
            whileHover={{ scale: 1.05 }}
            className="text-lg text-gray-300 font-elegant italic"
          >
            &quot;Every review is a testament to the power of AI companionship and genuine connection&quot;
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}
