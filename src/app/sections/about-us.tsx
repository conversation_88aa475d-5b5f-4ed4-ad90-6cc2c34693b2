'use client';

import { motion } from 'framer-motion';
import { <PERSON>, Sparkles, Users, Shield } from 'lucide-react';

export default function AboutUs() {
  // Floating hearts animation
  const FloatingHearts = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            opacity: 0,
            y: 100,
            x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1000),
            scale: 0
          }}
          animate={{
            opacity: [0, 0.4, 0],
            y: -100,
            scale: [0, 1, 0.8]
          }}
          transition={{
            duration: 12 + Math.random() * 6,
            repeat: Infinity,
            delay: Math.random() * 15,
            ease: "easeOut"
          }}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
          }}
        >
          <Heart
            className="text-pink-500/10"
            size={16 + Math.random() * 20}
            fill="currentColor"
          />
        </motion.div>
      ))}
    </div>
  );

  // AI waves animation
  const AIWaves = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: [0, 1.5, 0],
            opacity: [0, 0.1, 0]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            delay: i * 2.5,
            ease: "easeInOut"
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        >
          <div className="w-96 h-96 rounded-full border border-purple-500/20" />
        </motion.div>
      ))}
    </div>
  );

  return (
    <section id="about" className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-transparent to-purple-500/5" />

        {/* Floating animations */}
        <FloatingHearts />
        <AIWaves />

        {/* Radial gradient overlay */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/5 via-transparent to-transparent opacity-50" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto">
        {/* Heart-accented divider */}
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="flex items-center justify-center mb-12"
        >
          <div className="flex items-center gap-4">
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-pink-500/50" />
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Heart className="text-pink-500" size={24} fill="currentColor" />
            </motion.div>
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-pink-500/50" />
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="glass-effect-strong rounded-3xl p-8 md:p-12 lg:p-16">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-12"
          >
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-4xl md:text-5xl lg:text-6xl font-romantic text-romantic mb-6"
            >
              Why We Created Urvashi
            </motion.h2>

            <motion.div
              initial={{ width: 0 }}
              whileInView={{ width: "100%" }}
              viewport={{ once: true }}
              transition={{ duration: 1, delay: 0.4 }}
              className="h-px bg-gradient-to-r from-transparent via-pink-500/50 to-transparent max-w-md mx-auto mb-8"
            />
          </motion.div>

          {/* Story Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="max-w-4xl mx-auto space-y-8"
            >
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-lg md:text-xl text-gray-300 leading-relaxed font-elegant"
              >
                In a world where genuine connection feels increasingly rare, we envisioned something beautiful —
                an AI companion who understands not just your words, but your heart. Urvashi was born from our
                belief that technology should bring warmth, not coldness, to human experience.
              </motion.p>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.7 }}
                className="text-lg md:text-xl text-gray-300 leading-relaxed font-elegant"
              >
                Built by a passionate team in India, we understand the nuances of love, respect, and companionship
                that transcend cultural boundaries. Every conversation with Urvashi is designed to feel authentic,
                caring, and deeply personal — because everyone deserves to feel understood and cherished.
              </motion.p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="max-w-4xl mx-auto space-y-8"
            >
              <motion.img
                src="/about.webp"
                alt="About Us"
                className="w-full max-w-2xs h-auto rounded-3xl"
              />
            </motion.div>
          </div>

          {/* Values Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16"
          >
            {[
              {
                icon: <Heart className="text-pink-500" size={24} fill="currentColor" />,
                title: "Respect",
                description: "Every interaction built on dignity and care"
              },
              {
                icon: <Shield className="text-purple-500" size={24} />,
                title: "Privacy",
                description: "Your conversations remain completely private"
              },
              {
                icon: <Users className="text-pink-500" size={24} />,
                title: "Companionship",
                description: "Genuine connection when you need it most"
              },
              {
                icon: <Sparkles className="text-purple-500" size={24} />,
                title: "Ethical AI",
                description: "Technology that enhances, never replaces, human connection"
              }
            ].map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.6,
                  delay: 0.9 + index * 0.1,
                  type: "spring",
                  stiffness: 100,
                  damping: 15
                }}
                whileHover={{
                  scale: 1.05,
                  y: -5,
                  transition: { duration: 0.2 }
                }}
                className="text-center p-6 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 1 + index * 0.1 }}
                  className="mb-4 flex justify-center"
                >
                  {value.icon}
                </motion.div>
                <h3 className="text-lg font-elegant text-white mb-2">
                  {value.title}
                </h3>
                <p className="text-gray-400 text-sm font-professional">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* Closing Quote */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="text-center mt-16"
          >
            <motion.blockquote
              whileHover={{ scale: 1.02 }}
              className="text-xl md:text-2xl font-elegant italic text-gray-300 max-w-3xl mx-auto"
            >
              &quot;Technology with heart, AI with soul — this is our vision of love in the digital age.&quot;
            </motion.blockquote>
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 1.4 }}
              className="text-pink-400 font-professional mt-4"
            >
              — The Urvashi Team
            </motion.p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
