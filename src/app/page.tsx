'use client';

import { useState } from 'react';
import Hero from './sections/hero';
import Preloader from '@/components/Preloader';
import Navigation from '@/components/Navigation';
import Features from './sections/features';
import AppScreenshots from './sections/app-screenshots';
import UserReviews from './sections/user-reviews';
import CallToAction from './sections/call-to-action';
import AboutUs from './sections/about-us';
import ContactUs from './sections/contact-us';
import Footer from './sections/footer';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  const handlePreloaderComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <Preloader onComplete={handlePreloaderComplete} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      <Navigation />

      {/* Hero Section */}
      <Hero />

      {/* Features Section */}
      <Features />

      {/* App Screenshots Showcase Section */}
      <AppScreenshots />

      {/* User Reviews/Testimonials Section */}
      <UserReviews />

      {/* About Us Section */}
      <AboutUs />

      {/* Contact Us Section */}
      <ContactUs />

      {/* Call to Action Section */}
      <CallToAction />

      {/* Footer Section */}
      <Footer />
    </div>
  );
}
